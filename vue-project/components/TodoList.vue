<script setup lang="ts">
import Input from '~/components/Input.vue';
import Badge from '~/components/Badge.vue';
import { useClass } from '../../ui/adapters/vue';
import { TodoList } from '../../ui/todo-list/todo-list';

const todoList = useClass(new TodoList());
</script>

<template>
    <div>
        <div class="flex items-center gap-2">
            <Input v-model="todoList.input" placeholder="Add todo" />
            <Button @click="todoList.add">Add</Button>
        </div>

        <!-- Filter badges -->
        <div class="flex items-center gap-2 mt-4">
            <span class="text-sm font-medium text-gray-700">Filter:</span>
            <Badge :active="todoList.showingAll" @click="todoList.showAll"> All </Badge>
            <Badge :active="todoList.showingIncomplete" @click="todoList.showIncomplete"> Incomplete </Badge>
            <Badge :active="todoList.showingCompleted" @click="todoList.showCompleted"> Completed </Badge>
        </div>

        <div class="divide-y bg-white border border-gray-200 divide-gray-200 rounded-lg mt-4">
            <TodoItem
                v-for="todo of todoList.items"
                :key="todo.id.value"
                :completed="todo.completed"
                :text="todo.text"
                @toggle="todoList.toggle(todo)"
                @remove="todoList.remove(todo)"
            />
        </div>
    </div>
</template>
